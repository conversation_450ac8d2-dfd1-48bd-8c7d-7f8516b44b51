import { DiscoveryApi } from '@backstage/core-plugin-api';

export interface Collection {
  id: string;
  name: string;
  description: string;
  owner_id: string;
  created_at: string;
  updated_at: string;
  content?: string; // Only included in detailed view
  version?: number; // Only included in detailed view
}

export interface CollectionVersion {
  id: string;
  collection_id: string;
  version: number;
  content: string;
  created_at: string;
  user_id: string;
}

export interface CollectionCreateRequest {
  name: string;
  description: string;
  content: string;
}

export interface CollectionUpdateRequest {
  name?: string;
  description?: string;
  content?: string;
}

// API Testing Types
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS';

export interface TestResult {
  id: string;
  name: string;
  passed: boolean;
  error?: string;
  duration: number;
}

export interface ApiRequest {
  id: string;
  name: string;
  method: HttpMethod;
  url: string;
  headers: { key: string; value: string; enabled: boolean }[];
  params: { key: string; value: string; enabled: boolean }[];
  body: {
    mode: 'none' | 'raw' | 'form-data' | 'urlencoded';
    raw?: string;
    formData?: { key: string; value: string; type: string }[];
    urlencoded?: { key: string; value: string }[];
    enabled?: boolean; // Whether the body is enabled
  };
  auth?: {
    type: 'none' | 'basic' | 'bearer' | 'apiKey';
    basic?: { username: string; password: string };
    bearer?: { token: string };
    apiKey?: { key: string; value: string; in: 'header' | 'query' };
  };
  preRequestScript?: string; // Pre-request script content
  testScript?: string; // Test script content
  lastTestResults?: TestResult[]; // Results from the last test run
}

export interface ApiResponse {
  status: number;
  statusText: string;
  headers: Record<string, string>;
  body: string;
  time: number; // in milliseconds
  size: number;
  error?: string;
  url?: string;
}

export interface ApiEnvironment {
  id: string;
  name: string;
  variables: { key: string; value: string; enabled: boolean }[];
}

export interface ApiFolder {
  id: string;
  name: string;
  parentId?: string;
  requests: string[]; // Array of request IDs
  folders: ApiFolder[]; // Array of subfolders
}

export interface ApiCollection {
  id: string;
  name: string;
  description: string;
  folders: ApiFolder[];
  requests: Record<string, ApiRequest>;
  environments: ApiEnvironment[];
  _orphanedRequests?: string[]; // For debugging - requests that couldn't be added to folders
}

// K6 Converter Types
export type K6FileType = 'actions' | 'scenario' | 'config' | 'types' | 'combined';

export interface K6ScriptOutput {
  filename: string;
  content: string;
  folderPath?: string;
  fileType?: K6FileType;
}

export interface K6ConverterConfig {
  vus: number;
  duration: string;
  includeChecks: boolean;
  includeSleep: boolean;
  outputFormat: 'single' | 'multiple';
  selectionMode: 'single' | 'multiple';
  fileFormat: 'split' | 'combined';
}

// Postman Collection Types (for import/conversion)
export interface PostmanRequest {
  method: string;
  url: string | { raw: string; host?: string[]; path?: string[]; protocol?: string };
  header?: Array<{ key: string; value: string; disabled?: boolean }>;
  body?: {
    mode?: string;
    raw?: string;
    formdata?: Array<{ key: string; value: string; type: string }>;
    urlencoded?: Array<{ key: string; value: string; type: string }>;
  };
  auth?: {
    type: string;
    basic?: Array<{ key: string; value: string }>;
    bearer?: Array<{ key: string; value: string }>;
  };
}

export interface PostmanItem {
  name: string;
  request?: PostmanRequest;
  item?: PostmanItem[];
  id?: string;
}

export interface PostmanCollection {
  info: {
    name: string;
    description?: string;
    schema?: string;
  };
  item: PostmanItem[];
}

export interface OldPostmanCollection {
  name: string;
  description?: string;
  requests?: any[];
  folders?: any[];
}

// Custom Headers for K6 tests
export interface CustomHeaders {
  token: string;
  deviceId: string;
  [key: string]: string;
}

// UI Component Props Types
export interface FolderSelectorItem {
  id: string;
  name: string;
  path: string;
}

// Hook Return Types
export interface UseK6ConverterProps {
  selectedCollectionId: string;
  fetchCollectionById: (id: string) => Promise<Collection | null>;
}

export interface UseCollectionsReturn {
  collections: Collection[];
  loading: boolean;
  error?: Error;
  fetchCollectionById: (id: string) => Promise<Collection | null>;
}

export interface UseApiCollectionsReturn {
  collections: ApiCollection[];
  setCollections: (collections: ApiCollection[]) => void;
  collectionsLoading: boolean;
  collectionsError?: Error;
  expandedFolders: Record<string, boolean>;
  selectedItemId: string | null;
  setSelectedItemId: (id: string | null) => void;
  handleFolderToggle: (folderId: string) => void;
  handleItemSelect: (itemId: string) => void;
  handleAddCollection: () => void;
  handleDeleteCollection: (collectionId: string) => void;
  handleImportCollection: (collection: ApiCollection) => void;
  handleAddFolder: (collectionId: string, parentFolderId?: string, name?: string) => void;
  handleAddRequest: (collectionId: string, folderId?: string, name?: string, method?: HttpMethod, url?: string) => void;
  handleRenameCollection: (collectionId: string, newName: string) => void;
  handleRenameFolder: (collectionId: string, folderId: string, newName: string) => void;
  handleRenameRequest: (collectionId: string, requestId: string, newName: string) => void;
}

export interface UseRequestReturn {
  currentRequest: ApiRequest;
  setCurrentRequest: (request: ApiRequest) => void;
  currentResponse: ApiResponse | null;
  isLoading: boolean;
  tabValue: number;
  responseTabValue: number;
  isGeneratingTests: boolean;
  isRunningTests: boolean;
  testResults: any[] | null;
  testError: string | null;
  isSavingPreRequestScript: boolean;
  preRequestScriptError: string | null;
  handleTabChange: (event: React.ChangeEvent<{}>, newValue: number) => void;
  handleResponseTabChange: (event: React.ChangeEvent<{}>, newValue: number) => void;
  handleMethodChange: (method: HttpMethod) => void;
  handleUrlChange: (url: string) => void;
  handleSendRequest: (environment?: ApiEnvironment) => Promise<{ success: boolean; response?: ApiResponse; error?: any }>;
  handleGenerateTests: () => Promise<{ success: boolean; tests?: string; error?: any }>;
  handleRunTests: () => Promise<{ success: boolean; results?: any[]; error?: any }>;
  handleSaveTests: (tests: string) => Promise<{ success: boolean; error?: any }>;
  handleSavePreRequestScript: (script: string) => Promise<{ success: boolean; error?: any }>;
}

export interface UseEnvironmentsReturn {
  environments: ApiEnvironment[];
  currentEnvironment: string;
  setCurrentEnvironment: (environmentId: string) => void;
  isEnvironmentDialogOpen: boolean;
  setIsEnvironmentDialogOpen: (open: boolean) => void;
  environmentToEdit: ApiEnvironment | null;
  handleAddEnvironment: () => void;
  handleEditEnvironment: (environmentId: string) => void;
  handleSaveEnvironment: (environment: ApiEnvironment) => { success: boolean; environment: ApiEnvironment };
  handleDeleteEnvironment: (environmentId: string) => { success: boolean };
  handleImportEnvironment: (environment: ApiEnvironment) => { success: boolean; environment: ApiEnvironment };
}

export interface UseK6ConverterReturn {
  selectedCollection: Collection | null;
  folders: FolderSelectorItem[];
  selectedFolders: string[];
  k6Scripts: K6ScriptOutput[];
  activeScriptIndex: number;
  isGenerating: boolean;
  configExpanded: boolean;
  config: K6ConverterConfig;
  setActiveScriptIndex: (index: number) => void;
  handleFolderSelect: (folderId: string) => void;
  handleConfigChange: (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleCheckboxChange: (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleSelectModeChange: (event: React.ChangeEvent<{ value: unknown }>) => void;
  setConfigExpanded: (expanded: boolean) => void;
  generateK6Script: () => void;
}

// Form Data Types
export interface KeyValuePair {
  key: string;
  value: string;
  enabled: boolean;
}

export interface FormDataItem {
  key: string;
  value: string;
  type: string;
}

// Dialog State Types
export interface DialogState {
  open: boolean;
  data?: any;
}

// Notification Types
export interface NotificationState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'info' | 'warning';
}

// Test Execution Types
export interface TestExecutionContext {
  request: ApiRequest;
  response: ApiResponse;
  environment?: ApiEnvironment;
}

// Import/Export Types
export interface ImportResult {
  success: boolean;
  data?: ApiCollection | ApiEnvironment;
  error?: string;
}

export interface ExportOptions {
  format: 'json' | 'postman';
  includeEnvironments: boolean;
  selectedCollections?: string[];
}

// Component Props Types
export interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

export interface RenameDialogProps {
  open: boolean;
  onClose: () => void;
  onRename: (newName: string) => void;
  itemType: 'collection' | 'folder' | 'request';
  currentName: string;
  title?: string;
}

export interface ImportDialogProps {
  open: boolean;
  onClose: () => void;
  onImportCollection: (collection: ApiCollection) => void;
  onImportEnvironment: (environment: ApiEnvironment) => void;
}

export interface ExportDialogProps {
  open: boolean;
  onClose: () => void;
  collections: ApiCollection[];
  environments: ApiEnvironment[];
}

export interface CreateRequestDialogProps {
  open: boolean;
  onClose: () => void;
  onCreateRequest: (
    requestName: string,
    method: HttpMethod,
    url: string,
    parentId: string | null,
    collectionId: string
  ) => void;
  collections: ApiCollection[];
  selectedCollectionId?: string;
  selectedFolderId?: string;
}

export interface CreateFolderDialogProps {
  open: boolean;
  onClose: () => void;
  onCreateFolder: (folderName: string, parentId: string | null, collectionId: string) => void;
  collections: ApiCollection[];
  selectedCollectionId?: string;
  selectedFolderId?: string;
}

export interface EnvironmentDialogProps {
  open: boolean;
  onClose: () => void;
  environment?: ApiEnvironment;
  onSave: (environment: ApiEnvironment) => void;
}

export interface CollectionsSidebarProps {
  collections: ApiCollection[];
  collectionsLoading: boolean;
  collectionsError: Error | undefined;
  expandedFolders: Record<string, boolean>;
  selectedItemId: string | null;
  onFolderToggle: (folderId: string) => void;
  onItemSelect: (itemId: string) => void;
  onAddCollection: () => void;
  onDeleteCollection: (collectionId: string) => void;
  onEditCollection: (collectionId: string) => void;
  onAddFolder: (collectionId: string, parentFolderId?: string) => void;
  onAddRequest: (collectionId: string, folderId?: string) => void;
  onDeleteRequest: (requestId: string) => void;
  onDeleteFolder: (folderId: string) => void;
  onDuplicateRequest: (requestId: string) => void;
  onRenameCollection: (collectionId: string, newName: string) => void;
  onRenameFolder: (collectionId: string, folderId: string, newName: string) => void;
  onRenameRequest: (collectionId: string, requestId: string, newName: string) => void;
}

export interface EnvironmentSelectorProps {
  environments: ApiEnvironment[];
  currentEnvironment: string;
  onEnvironmentChange: (environmentId: string) => void;
  onAddEnvironment: () => void;
  onEditEnvironment: (environmentId: string) => void;
  onImportEnvironment: () => void;
}

export interface ParamsTabProps {
  params: KeyValuePair[];
  onChange: (params: KeyValuePair[]) => void;
}

export interface HeadersTabProps {
  headers: KeyValuePair[];
  onChange: (headers: KeyValuePair[]) => void;
}

export interface BodyTabProps {
  body: ApiRequest['body'];
  onChange: (body: ApiRequest['body']) => void;
}

// Delete Confirmation Types
export interface DeleteItem {
  id: string;
  type: 'collection' | 'request' | 'folder';
  name?: string;
}

// K6 Converter Component Props Types
export interface CollectionSelectorProps {
  collections: Collection[];
  selectedCollectionId: string;
  onCollectionChange: (event: React.ChangeEvent<{ value: unknown }>) => void;
}

export interface FolderSelectorProps {
  folders: FolderSelectorItem[];
  selectedFolders: string[];
  selectionMode: 'single' | 'multiple';
  onFolderSelect: (folderId: string) => void;
  onSelectionModeChange: (event: React.ChangeEvent<{ value: unknown }>) => void;
}

export interface ConfigurationPanelProps {
  expanded: boolean;
  onExpandChange: () => void;
  vus: number;
  duration: string;
  includeChecks: boolean;
  includeSleep: boolean;
  onConfigChange: (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => void;
  onCheckboxChange: (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => void;
}

export interface ScriptOutputPanelProps {
  scripts: K6ScriptOutput[];
  activeScriptIndex: number;
  onTabChange: (newIndex: number) => void;
  onCopyToClipboard: (content: string) => void;
}

export interface ScriptTabsProps {
  scripts: K6ScriptOutput[];
  activeIndex: number;
  onTabChange: (newIndex: number) => void;
}

export interface GenerateScriptButtonProps {
  isGenerating: boolean;
  onClick: () => void;
}

// Utility Types
export interface CopyToClipboardResult {
  success: boolean;
  message: string;
}

// Error Types
export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}

// Loading States
export interface LoadingState {
  isLoading: boolean;
  error?: string | Error;
}

// Pagination Types
export interface PaginationOptions {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Service Types
export interface ProxyServiceApi {
  readonly discoveryApi: DiscoveryApi;
  fetch(url: string, options?: RequestInit, useProxy?: boolean): Promise<Response>;
  isInternalUrl(url: string): boolean;
}

export interface ImportExportServiceOptions {
  format?: 'json' | 'postman';
  includeEnvironments?: boolean;
}

// Test Execution Types
export interface TestResult {
  id: string;
  name: string;
  passed: boolean;
  error?: string;
  responseValue?: any;
  testType?: string;
  duration: number;
}

export interface TestSandbox {
  pm: {
    response: any;
    test: (name: string, testFn: () => void) => void;
    expect: any;
  };
}

// Utility Function Types
export interface ConversionResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  warnings?: string[];
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Request/Response Processing Types
export interface ProcessedRequest {
  id: string;
  originalRequest: ApiRequest;
  processedUrl: string;
  processedHeaders: Record<string, string>;
  processedBody?: string;
  environment?: ApiEnvironment;
}

export interface ProcessedResponse {
  originalResponse: ApiResponse;
  parsedBody?: any;
  extractedData?: Record<string, any>;
  validationResults?: ValidationResult;
}

// File Processing Types
export interface FileUploadResult {
  success: boolean;
  filename: string;
  size: number;
  content?: string;
  error?: string;
}

export interface FileExportResult {
  success: boolean;
  filename: string;
  content: string;
  mimeType: string;
  error?: string;
}

// Environment Variable Processing
export interface VariableResolution {
  original: string;
  resolved: string;
  variables: Record<string, string>;
  unresolvedVariables: string[];
}

// Collection Processing Types
export interface CollectionStats {
  totalRequests: number;
  totalFolders: number;
  requestsByMethod: Record<HttpMethod, number>;
  averageResponseTime?: number;
  successRate?: number;
}

export interface CollectionValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  stats: CollectionStats;
}
