import React, { useState, useCallback } from 'react';
import {
  Grid,
  Typography,
  Snackbar,
  makeStyles,
} from '@material-ui/core';
import { InfoCard } from '@backstage/core-components';
import { Alert } from '@material-ui/lab';
import { useApi, errorApiRef } from '@backstage/core-plugin-api';

// Custom hooks
import { useCollections } from './hooks/useCollections';
import { useRequest } from './hooks/useRequest';
import { useEnvironments } from './hooks/useEnvironments';

// Components
import { CollectionsSidebar } from './components/CollectionsSidebar';
import { RequestPanel } from './components/RequestPanel';
import { ResponsePanel } from './components/ResponsePanel';
import { EnvironmentSelector } from './components/EnvironmentSelector';
import { ImportDialog } from './ImportDialog';
import { ExportDialog } from './ExportDialog';
import { EnvironmentDialog } from './EnvironmentDialog';
import { CreateRequestDialog } from './CreateRequestDialog';
import { CreateFolderDialog } from './CreateFolderDialog';

// Types
import { postmanConverterApiRef } from '../../api';

const useStyles = makeStyles(theme => ({
  root: {
    height: '100%',
  },
  infoCard: {
    marginBottom: theme.spacing(3),
  },
  paper: {
    padding: theme.spacing(2),
    height: '100%',
  },
}));

export const ApiTestingPage = () => {
  const classes = useStyles();
  const errorApi = useApi(errorApiRef);
  const postmanConverterApi = useApi(postmanConverterApiRef);

  // Use custom hooks for state management
  const {
    collections,
    setCollections,
    collectionsLoading,
    collectionsError,
    expandedFolders,
    selectedItemId,
    setSelectedItemId,
    handleFolderToggle,
    handleItemSelect,
    handleAddCollection,
    handleDeleteCollection,
    handleImportCollection,
    handleAddFolder,
    handleAddRequest,
    handleRenameCollection,
    handleRenameFolder,
    handleRenameRequest,
  } = useCollections();

  const {
    currentRequest,
    setCurrentRequest,
    currentResponse,
    isLoading,
    tabValue,
    responseTabValue,
    isGeneratingTests,
    isRunningTests,
    testResults,
    testError,
    isSavingPreRequestScript,
    preRequestScriptError,
    handleTabChange,
    handleResponseTabChange,
    handleMethodChange,
    handleUrlChange,
    handleSendRequest,
    handleGenerateTests,
    handleRunTests,
    handleSaveTests,
    handleSavePreRequestScript,
  } = useRequest(collections, setCollections);

  const {
    environments,
    currentEnvironment,
    setCurrentEnvironment,
    isEnvironmentDialogOpen,
    setIsEnvironmentDialogOpen,
    environmentToEdit,
    handleAddEnvironment,
    handleEditEnvironment,
    handleSaveEnvironment,
    handleDeleteEnvironment,
    handleImportEnvironment,
  } = useEnvironments();

  // State for dialogs
  const [isImportDialogOpen, setIsImportDialogOpen] = useState<boolean>(false);
  const [isExportDialogOpen, setIsExportDialogOpen] = useState<boolean>(false);
  const [isCreateRequestDialogOpen, setIsCreateRequestDialogOpen] = useState<boolean>(false);
  const [isCreateFolderDialogOpen, setIsCreateFolderDialogOpen] = useState<boolean>(false);
  const [selectedCollectionId, setSelectedCollectionId] = useState<string>('');
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);

  // State for snackbar
  const [snackbar, setSnackbar] = useState<{open: boolean, message: string, severity: 'success' | 'error' | 'info' | 'warning'}>({
    open: false,
    message: '',
    severity: 'info',
  });

  // Handle snackbar close
  const handleSnackbarClose = useCallback(() => {
    setSnackbar(prev => ({ ...prev, open: false }));
  }, []);

  // Handle dialog actions
  const handleOpenImportDialog = useCallback(() => setIsImportDialogOpen(true), []);
  const handleCloseImportDialog = useCallback(() => setIsImportDialogOpen(false), []);
  const handleOpenExportDialog = useCallback(() => setIsExportDialogOpen(true), []);
  const handleCloseExportDialog = useCallback(() => setIsExportDialogOpen(false), []);

  // Handle request selection from sidebar
  const handleRequestSelect = useCallback((requestId: string) => {
    const request = collections.find(c => c.requests[requestId])?.requests[requestId];
    if (request) {
      setCurrentRequest(request);
      setSelectedItemId(requestId);
    }
  }, [collections, setCurrentRequest, setSelectedItemId]);

  // Handle collection actions
  const handleCreateFolder = useCallback((collectionId: string, parentFolderId?: string) => {
    setSelectedCollectionId(collectionId);
    setSelectedFolderId(parentFolderId || null);
    setIsCreateFolderDialogOpen(true);
  }, []);

  const handleCreateRequest = useCallback((collectionId: string, folderId?: string) => {
    setSelectedCollectionId(collectionId);
    setSelectedFolderId(folderId || null);
    setIsCreateRequestDialogOpen(true);
  }, []);

  // Placeholder functions for missing handlers
  const handleEditCollection = useCallback((collectionId: string) => {
    console.log('Edit collection:', collectionId);
  }, []);

  const handleDeleteRequest = useCallback((requestId: string) => {
    console.log('Delete request:', requestId);
  }, []);

  const handleDeleteFolder = useCallback((folderId: string) => {
    console.log('Delete folder:', folderId);
  }, []);

  const handleDuplicateRequest = useCallback((requestId: string) => {
    console.log('Duplicate request:', requestId);
  }, []);

  // Get current environment for request execution
  const selectedEnvironment = environments.find(env => env.id === currentEnvironment);

  return (
    <div className={classes.root}>
      <InfoCard title="API Testing" className={classes.infoCard}>
        <Typography variant="body1">
          Test your APIs with a comprehensive testing interface. Create requests, organize them in collections,
          and execute tests with environment variable support.
        </Typography>
      </InfoCard>

      <Grid container spacing={3}>
        {/* Collections Sidebar */}
        <Grid item xs={12} md={3}>
          <CollectionsSidebar
            collections={collections}
            collectionsLoading={collectionsLoading}
            collectionsError={collectionsError}
            expandedFolders={expandedFolders}
            selectedItemId={selectedItemId}
            onFolderToggle={handleFolderToggle}
            onItemSelect={handleRequestSelect}
            onAddCollection={handleAddCollection}
            onDeleteCollection={handleDeleteCollection}
            onEditCollection={handleEditCollection}
            onAddFolder={handleCreateFolder}
            onAddRequest={handleCreateRequest}
            onDeleteRequest={handleDeleteRequest}
            onDeleteFolder={handleDeleteFolder}
            onDuplicateRequest={handleDuplicateRequest}
            onRenameCollection={handleRenameCollection}
            onRenameFolder={handleRenameFolder}
            onRenameRequest={handleRenameRequest}
          />
        </Grid>

        {/* Main Content */}
        <Grid item xs={12} md={9}>
          <Grid container spacing={3}>
            {/* Environment Selector */}
            <Grid item xs={12}>
              <EnvironmentSelector
                environments={environments}
                currentEnvironment={currentEnvironment}
                onEnvironmentChange={setCurrentEnvironment}
                onAddEnvironment={handleAddEnvironment}
                onEditEnvironment={handleEditEnvironment}
                onImportEnvironment={handleImportEnvironment}
              />
            </Grid>

            {/* Request Panel */}
            <Grid item xs={12}>
              <RequestPanel
                request={currentRequest}
                response={currentResponse}
                isLoading={isLoading}
                tabValue={tabValue}
                onTabChange={handleTabChange}
                onMethodChange={handleMethodChange}
                onUrlChange={handleUrlChange}
                onSendRequest={() => handleSendRequest(selectedEnvironment)}
                onUpdateRequest={setCurrentRequest}
                testResults={testResults}
                isGeneratingTests={isGeneratingTests}
                isRunningTests={isRunningTests}
                testError={testError}
                onGenerateTests={handleGenerateTests}
                onRunTests={handleRunTests}
                onSaveTests={handleSaveTests}
                isSavingPreRequestScript={isSavingPreRequestScript}
                preRequestScriptError={preRequestScriptError}
                onSavePreRequestScript={handleSavePreRequestScript}
                onSaveRequest={() => console.log('Save request')}
                currentEnvironment={selectedEnvironment}
              />
            </Grid>

            {/* Response Panel */}
            {currentResponse && (
              <Grid item xs={12}>
                <ResponsePanel
                  response={currentResponse}
                  tabValue={responseTabValue}
                  onTabChange={handleResponseTabChange}
                />
              </Grid>
            )}
          </Grid>
        </Grid>
      </Grid>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Import Dialog */}
      <ImportDialog
        open={isImportDialogOpen}
        onClose={handleCloseImportDialog}
        onImportCollection={handleImportCollection}
        onImportEnvironment={handleImportEnvironment}
      />

      {/* Export Dialog */}
      <ExportDialog
        open={isExportDialogOpen}
        onClose={handleCloseExportDialog}
        collections={collections}
        environments={environments}
      />

      {/* Environment Dialog */}
      <EnvironmentDialog
        open={isEnvironmentDialogOpen}
        onClose={() => setIsEnvironmentDialogOpen(false)}
        environment={environmentToEdit}
        onSave={handleSaveEnvironment}
      />

      {/* Create Request Dialog */}
      <CreateRequestDialog
        open={isCreateRequestDialogOpen}
        onClose={() => setIsCreateRequestDialogOpen(false)}
        onCreateRequest={(name, method, url) => {
          handleAddRequest(selectedCollectionId, selectedFolderId || undefined, name, method, url);
          setIsCreateRequestDialogOpen(false);
        }}
        collections={collections}
        selectedCollectionId={selectedCollectionId}
        selectedFolderId={selectedFolderId || undefined}
      />

      {/* Create Folder Dialog */}
      <CreateFolderDialog
        open={isCreateFolderDialogOpen}
        onClose={() => setIsCreateFolderDialogOpen(false)}
        onCreateFolder={(name) => {
          handleAddFolder(selectedCollectionId, selectedFolderId || undefined, name);
          setIsCreateFolderDialogOpen(false);
        }}
        collections={collections}
        selectedCollectionId={selectedCollectionId}
        selectedFolderId={selectedFolderId || undefined}
      />
    </div>
  );
};